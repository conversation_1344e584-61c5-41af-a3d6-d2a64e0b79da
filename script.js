marked.setOptions({ breaks: true, smartyPants: true });

class J2M {
  static text_conversion(str) {
    if (false && true) {
      return marked.parse(str);
    } else if (true && false) {
      return J2M.to_markdown(str);
    } else if (true && true) {
      return marked.parse(J2M.to_markdown(str));
    } else {
      Error("Nieprawidłowe wykorzystanie bloku - nieobsługiwany typ konwersji");
    }
  }

  static to_markdown(str) {
    return (
      str
        // Un-Ordered Lists
        .replace(/^[ \t]*(\*+)\s+/gm, (match, stars) => {
          return `${Array(stars.length).join("  ")}* `;
        })
        // Ordered lists
        .replace(/^[ \t]*(#+)\s+/gm, (match, nums) => {
          return `${Array(nums.length).join("   ")}1. `;
        })
        // Headers 1-6
        .replace(/^h([0-6])\.(.*)$/gm, (match, level, content) => {
          return Array(parseInt(level, 10) + 1).join("#") + content;
        })
        // Monospaced text
        //.replace(/\{\{([*_^+\-~]*)([^_*^+\-~]+)([*_^+\-~]*)\}\}/g, '$1`$2`$3')
        //.replace(/\{\{([*_^+\-~]*)((?:\s|.)+?)([*_^+\-~]*)\}\}/g, "$1`$2`$3")
        .replace(/\{\{([^}]+)\}\}/g, '`$1`')
        // Bold
        .replace(/\*(\S.*)\*/g, "**$1**")
        // Italic
        .replace(/_(\S.*)_/g, "*$1*")
        // Citations (buggy)
        .replace(
          /\?\?([*_^+\-~]*)((?:\s|.)+?)([*_^+\-~]*)\?\?/g,
          "$1<cite>$2</cite>$3"
        )
        // Inserts
        .replace(/\+([^+]*)\+/g, "<ins>$1</ins>")
        // Superscript
        .replace(/\^([^^]*)\^/g, "<sup>$1</sup>")
        // Subscript
        .replace(/~([^~]*)~/g, "<sub>$1</sub>")
        // Strikethrough
        .replace(
          /(\s+|[*;^;_;+;~])-(\S+.*?\S)-(\s{1}|\n|$|[*;^;_;+;~])/g,
          "$1~~$2~~$3"
        )
        // Code Block
        .replace(
          /\{code(:([a-z]+))?([:|]?(title|borderStyle|borderColor|borderWidth|bgColor|titleBGColor)=.+?)*\}([^]*?)\n?\{code\}/gm,
          "```$2$5\n```"
        )
        // Pre-formatted text
        .replace(/{noformat}/g, "```")
        // Un-named Links
        .replace(/\[([^|]+?)\]/g, "<$1>")
        // Images
        .replace(/!(.+)!/g, "![]($1)")
        // Named Links
        .replace(/\[(.+?)\|(.+?)\]/g, "[$1]($2)")
        // Single Paragraph Blockquote
        .replace(/^bq\.\s+/gm, "> ")
        // Remove color: unsupported in md
        .replace(/\{color:[^}]+\}([^]*?)\{color\}/gm, "$1")
        // panel into table
        .replace(
          /\{panel:title=([^}]*)\}\n?([^]*?)\n?\{panel\}/gm,
          "\n| $1 |\n| --- |\n| $2 |"
        )
        // table header
        .replace(/^[ \t]*((?:\|\|.*?)+\|\|)[ \t]*$/gm, (match, headers) => {
          const singleBarred = headers.replace(/\|\|/g, "|");
          return `\n${singleBarred}\n${singleBarred.replace(
            /\|[^|]+/g,
            "| --- "
          )}`;
        })
        // remove leading-space of table headers and rows
        .replace(/^[ \t]*\|/gm, "|")
    );
  }
}

var text =
  "*Project objectives:* * Retain Business Continuity and positive UX * Detailed Microsoft services (and model of delivery) that will be in scope of TSA. * Detailed dependencies related to current security and compliance requirements and Cyber tools * Prepare TSA input for SWAN services needed to be continued under TSA * Sign contracts for Microsoft and other vendors products/services which no longer will be provided by SWAN * Define and confirm Target Operating Model for Day 1 * Provide processes procedures and mechanisms required for a smooth transition of Microsoft environment and dependencies on Day 1";

var text2 = `Projekt w obszarze finansów realizowany w ramach 4 strumieni prac:  h1. *1. Controlling*    
*Project objectives:*   * Perform mapping of selected D1 BITA DDS (Account, Deal, Controlling Steering Tables, 
  Organization Units and Customer Relationship Managers, Deal Connections, Controlling Parameters, Fees & Bank services, Costs), 
  products and fees dictionaries mapping   * Development of reconciliation process between controlling and accounting data   
  * Provide required data for Eagle reporting on D1 and next 6-12 months in excel templates (e.g. GCR)   
  * Provide financial plans to Eagle in excel templates (e.g. UBT) in line with adopted compliance procedures   
  * * Restatement of 2025 results and plans for following years according to new rules adopted by Pelican   
  * * Harmonization of Pelican's processes and policies with those in Eagle (including benchmarking)   
  * * Development of local solution for PPNR   * Controlling related agreements termination marked as Terminate at Closing/Now    
  * *Risks / considerations:*   * Data completeness & consistency   * Resources on Bank's side   * Challenging timeframe for delivery   
  * * Legal risks (data&information sharing)   * Final scope not fully known   * Required support of DWH and Business    
  * h1. *2. Accounting & Obligatory Reporting*    {*}Project objectives:{*}{*}{{*}}   * Implementation of PPA methodology 
  * by Pelican for areas other than ECL, tool & calculation process implementation   * Perform mapping Pelican chart of accounts to Eagle GCoA   * Direct delivery of data by Pelican to Eagle for reporting of non-risk templates for FINREP and  Pillar III (except part covered in Risk Obligatory Reporting & cRWA/cCFC/NPE backstop workstream), COREP, CSRD, Large Exposure, Leverage Ratio in line with adopted compliance procedure   * Perform mapping of selected D1 BITA DDS (Major Amounts and NOCU, Tax and Other Assets, Tax and Other Liabilities, Equity Products)   * Accounting related agreements termination marked as Terminate at Closing/Now   * Ensuring the continuity of the rating    *Risks / considerations:*   * Challenging timeframe for delivery   * Data completeness & consistency   * Data Warehouse performance   * Legal risks (data&information sharing)       h1. *3. Risk ECL/FV calculation for PPA*    *Project objectives:*   * Definition of ECL methodology (incl. level of methodology alignment between Pelican and Eagle for D-1 and after D-1), tool & calculation process implementation to be used for D1 and after D1 (monthly ECL calculation)   * Definition of FV methodology & calculation for PPA   * Subsequent measurement of ECL and related metrics (e.g. risk costs,)    *Risks / considerations:*   * Challenging timeframe for delivery   * Final scope not fully known   * Legal risks (data&information sharing)    h1. *4. Risk Obligatory Reporting & cRWA/cCFC/NPE backstop*    *Project objectives:*    Perform tasks on a Pelican's side only:   * Ensure cRWA, Carbon Footprint Calculation (CFC) and NPE backstop calculation for Eagle purposes   * Direct delivery of data by Pelican to Eagle for reporting of:   ** FINREP risk templates   ** IFRS7 risk reporting   ** ESG data (EPC, financed emissions)  relevant for Pillar 3, GRR and SREP reporting   ** Pillar 3 disclosure – templates not covered by COREP or FINREP   ** Reporting towards ECB in the course of SREP process – risk templates   ** Data for risk analysis/ monitoring and reporting for Eagle internal purposes    *Risks / considerations:*   * Challenging timeframe for delivery   * Final scope not fully known   * Legal risks (data&information sharing)         Link do BC:    [TRIAGE-568 P041 Finance|https://santandernet.sharepoint.com/:f:/r/sites/ZespAnaliziTransformacjiModeluBiznesowego-BusinessCase/Shared%20Documents/Amadeusz/TRIAGE-568%20P041%20Finance?csf=1&web=1&e=uWDfbx]`;

try {
  var formattedText = J2M.text_conversion(text2);
} catch (error) {
  console.error(error);
}

console.log(formattedText);
